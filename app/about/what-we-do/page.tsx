import Image from 'next/image'
import Link from 'next/link'

export default function WhatWeDoPage() {
  return (
    <div className="min-h-screen bg-black text-white pt-24">
      <div className="max-w-7xl mx-auto px-4">
        {/* Hero Section */}
        <section className="py-16">
          <div className="text-center mb-16">
            <h1 className="text-5xl lg:text-7xl font-montserrat font-bold mb-6">What We Do</h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              We are transforming Africa's creative landscape by providing world-class education, 
              fostering innovation, and building bridges between talent and opportunity.
            </p>
          </div>
        </section>

        {/* Core Activities */}
        <section className="py-16">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Education & Training */}
            <div className="bg-gray-900 rounded-lg p-8">
              <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                </svg>
              </div>
              <h3 className="text-2xl font-montserrat font-bold mb-4">Education & Training</h3>
              <p className="text-gray-300 mb-6">
                We provide comprehensive training programs in filmmaking, acting, digital media, and creative arts. 
                Our curriculum combines theoretical knowledge with hands-on practical experience.
              </p>
              <ul className="text-gray-300 space-y-2">
                <li>• Professional filmmaking courses</li>
                <li>• Acting and performance training</li>
                <li>• Digital content creation</li>
                <li>• Technical skills development</li>
                <li>• Industry-standard certifications</li>
              </ul>
            </div>

            {/* Industry Partnerships */}
            <div className="bg-gray-900 rounded-lg p-8">
              <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="text-2xl font-montserrat font-bold mb-4">Industry Partnerships</h3>
              <p className="text-gray-300 mb-6">
                We collaborate with leading production companies, studios, and creative agencies to provide 
                real-world experience and direct pathways to employment.
              </p>
              <ul className="text-gray-300 space-y-2">
                <li>• Internship placements</li>
                <li>• Industry mentorship programs</li>
                <li>• Live project collaborations</li>
                <li>• Networking events and workshops</li>
                <li>• Job placement assistance</li>
              </ul>
            </div>

            {/* Creative Innovation */}
            <div className="bg-gray-900 rounded-lg p-8">
              <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="text-2xl font-montserrat font-bold mb-4">Creative Innovation</h3>
              <p className="text-gray-300 mb-6">
                We foster innovation through research, development of new creative methodologies, 
                and exploration of emerging technologies in media and entertainment.
              </p>
              <ul className="text-gray-300 space-y-2">
                <li>• Emerging technology integration</li>
                <li>• Creative research projects</li>
                <li>• Innovation labs and studios</li>
                <li>• Cross-cultural collaborations</li>
                <li>• Future-focused curriculum</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Our Impact */}
        <section className="py-16 bg-gray-900 rounded-2xl my-16">
          <div className="px-8">
            <h2 className="text-4xl font-montserrat font-bold text-center mb-12">Our Impact</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-5xl font-montserrat font-bold text-red-500 mb-2">3,000+</div>
                <p className="text-gray-300">Students Trained</p>
              </div>
              <div className="text-center">
                <div className="text-5xl font-montserrat font-bold text-red-500 mb-2">500+</div>
                <p className="text-gray-300">Films Produced</p>
              </div>
              <div className="text-center">
                <div className="text-5xl font-montserrat font-bold text-red-500 mb-2">15+</div>
                <p className="text-gray-300">Countries Reached</p>
              </div>
              <div className="text-center">
                <div className="text-5xl font-montserrat font-bold text-red-500 mb-2">85%</div>
                <p className="text-gray-300">Employment Rate</p>
              </div>
            </div>
          </div>
        </section>

        {/* Programs Overview */}
        <section className="py-16">
          <h2 className="text-4xl font-montserrat font-bold text-center mb-12">Our Programs</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-red-700 rounded-lg p-8 text-white">
              <h3 className="text-2xl font-montserrat font-bold mb-4">Professional Courses</h3>
              <p className="mb-6">
                Comprehensive training programs designed to prepare students for successful careers 
                in the creative industry.
              </p>
              <Link 
                href="/courses" 
                className="inline-block bg-white text-red-700 px-6 py-3 font-bebas tracking-wider hover:bg-gray-100 transition-colors"
              >
                VIEW COURSES
              </Link>
            </div>
            <div className="bg-black border border-gray-700 rounded-lg p-8">
              <h3 className="text-2xl font-montserrat font-bold mb-4">Special Programs</h3>
              <p className="text-gray-300 mb-6">
                Intensive workshops, masterclasses, and specialized training programs with 
                industry experts and international professionals.
              </p>
              <Link 
                href="/special-programs" 
                className="inline-block border border-white text-white px-6 py-3 font-bebas tracking-wider hover:bg-white hover:text-black transition-colors"
              >
                EXPLORE PROGRAMS
              </Link>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 text-center">
          <h2 className="text-4xl font-montserrat font-bold mb-6">Ready to Join Our Mission?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Be part of the movement that's transforming Africa's creative landscape. 
            Whether you're a student, industry professional, or creative enthusiast, 
            there's a place for you at Del York Creative Academy.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="https://portal.delyorkcreative.academy/student/apply"
              className="bg-red-600 text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-red-700 transition-colors"
            >
              START YOUR JOURNEY
            </Link>
            <Link
              href="/contact"
              className="border border-white text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-white hover:text-black transition-colors"
            >
              GET IN TOUCH
            </Link>
          </div>
        </section>
      </div>
    </div>
  )
}
