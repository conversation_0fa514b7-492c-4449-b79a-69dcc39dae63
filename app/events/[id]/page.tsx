import Link from "next/link";
import { Calendar, MapPin, Clock, Users, ArrowLeft, Share2 } from "lucide-react";
import { notFound } from "next/navigation";

// Static generation configuration
export const dynamic = 'force-static';
export const revalidate = false;

// Mock events data (same as in main events page)
const events = [
  {
    id: 1,
    title: "Creative Storytelling Workshop",
    description: "Learn the fundamentals of visual storytelling from industry professionals. This hands-on workshop covers narrative structure, character development, and visual composition.",
    fullDescription: "Join us for an intensive day-long workshop where you'll dive deep into the art of visual storytelling. Led by award-winning filmmakers and industry veterans, this workshop will cover everything from basic narrative structure to advanced visual composition techniques. You'll work on practical exercises, analyze case studies from successful films, and develop your own storytelling project. Perfect for aspiring filmmakers, content creators, and anyone looking to enhance their narrative skills.",
    date: "2024-09-15",
    time: "10:00 AM - 4:00 PM",
    location: "Del York Creative Academy Campus",
    category: "Workshop",
    image: "/images/events/storytelling-workshop.jpg",
    capacity: 25,
    registered: 18,
    price: "Free",
    featured: true,
    instructor: "<PERSON>",
    instructor<PERSON><PERSON>: "Award-winning filmmaker with over 15 years of experience in the industry.",
    requirements: ["Basic understanding of film/video", "Laptop or notebook for exercises", "Creative mindset"],
    agenda: [
      "9:30 AM - Registration & Welcome Coffee",
      "10:00 AM - Introduction to Visual Storytelling",
      "11:30 AM - Break",
      "12:00 PM - Narrative Structure Workshop",
      "1:00 PM - Lunch Break",
      "2:00 PM - Character Development Session",
      "3:30 PM - Break",
      "4:00 PM - Final Project Presentations"
    ]
  },
  // Add other events here if needed for the demo
];

interface EventPageProps {
  params: {
    id: string;
  };
}

export default function EventPage({ params }: EventPageProps) {
  const eventId = parseInt(params.id);
  const event = events.find(e => e.id === eventId);

  if (!event) {
    notFound();
  }

  const eventDate = new Date(event.date);
  const isUpcoming = eventDate > new Date();
  const spotsLeft = event.capacity - event.registered;

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Back Navigation */}
      <div className="max-w-7xl mx-auto px-4 pt-20 pb-8">
        <Link
          href="/events"
          className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Events
        </Link>
      </div>

      {/* Event Header */}
      <section className="relative py-16 px-4">
        <div className="absolute inset-0 bg-gradient-to-br from-red-900/20 to-black"></div>
        <div className="relative max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Event Image */}
            <div className="relative h-96 bg-gray-800 rounded-lg overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-red-600/20 to-gray-900/80 flex items-center justify-center">
                <Calendar className="h-24 w-24 text-gray-400" />
              </div>
              {event.featured && (
                <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Featured Event
                </div>
              )}
              <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
                {event.category}
              </div>
            </div>

            {/* Event Details */}
            <div>
              <h1 className="text-4xl md:text-5xl font-bebas tracking-wider text-white mb-6">
                {event.title}
              </h1>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-center text-gray-300">
                  <Calendar className="h-5 w-5 mr-3 text-red-400" />
                  {eventDate.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </div>
                
                <div className="flex items-center text-gray-300">
                  <Clock className="h-5 w-5 mr-3 text-red-400" />
                  {event.time}
                </div>
                
                <div className="flex items-center text-gray-300">
                  <MapPin className="h-5 w-5 mr-3 text-red-400" />
                  {event.location}
                </div>
                
                <div className="flex items-center text-gray-300">
                  <Users className="h-5 w-5 mr-3 text-red-400" />
                  {event.registered}/{event.capacity} registered
                  {spotsLeft > 0 && spotsLeft <= 10 && (
                    <span className="ml-2 text-orange-400 font-semibold">
                      ({spotsLeft} spots left!)
                    </span>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-4 mb-8">
                <div className="text-3xl font-bold text-white">
                  {event.price}
                </div>
                {spotsLeft > 0 ? (
                  <button className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    Register Now
                  </button>
                ) : (
                  <button className="bg-gray-600 text-gray-300 px-8 py-3 rounded-lg font-semibold cursor-not-allowed">
                    Event Full
                  </button>
                )}
                <button className="border border-gray-600 text-gray-300 hover:border-white hover:text-white px-4 py-3 rounded-lg transition-colors">
                  <Share2 className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Event Description */}
      <section className="py-16 px-4 bg-gray-900/30">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bebas tracking-wider mb-8">ABOUT THIS EVENT</h2>
          <p className="text-gray-300 text-lg leading-relaxed mb-8">
            {event.fullDescription || event.description}
          </p>

          {event.instructor && (
            <div className="bg-gray-800 rounded-lg p-6 mb-8">
              <h3 className="text-xl font-semibold text-white mb-2">Instructor</h3>
              <p className="text-red-400 font-semibold mb-2">{event.instructor}</p>
              <p className="text-gray-300">{event.instructorBio}</p>
            </div>
          )}

          {event.requirements && (
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-white mb-4">What to Bring</h3>
              <ul className="space-y-2">
                {event.requirements.map((req, index) => (
                  <li key={index} className="text-gray-300 flex items-center">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-3"></span>
                    {req}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {event.agenda && (
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Event Agenda</h3>
              <div className="space-y-3">
                {event.agenda.map((item, index) => (
                  <div key={index} className="flex items-center text-gray-300">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-3"></span>
                    {item}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 px-4 bg-gradient-to-r from-red-900/20 to-black">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bebas tracking-wider mb-6">
            READY TO JOIN US?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Don't miss out on this opportunity to enhance your creative skills and network with fellow professionals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {spotsLeft > 0 ? (
              <button className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                Register for This Event
              </button>
            ) : (
              <button className="bg-gray-600 text-gray-300 px-8 py-3 rounded-lg font-semibold cursor-not-allowed">
                Event Full - Join Waitlist
              </button>
            )}
            <Link
              href="/events"
              className="border border-white text-white hover:bg-white hover:text-black px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              View Other Events
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
