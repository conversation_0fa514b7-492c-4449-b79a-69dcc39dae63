import Image from 'next/image'

export default function InstructorsPage() {
  const instructors = [
    {
      name: "<PERSON>",
      specialty: "Film Direction",
      bio: "Award-winning director with 15+ years in the industry",
      image: "/placeholder.svg?height=300&width=300"
    },
    {
      name: "<PERSON>",
      specialty: "Acting Coach",
      bio: "Former Broadway actor turned dedicated acting instructor",
      image: "/placeholder.svg?height=300&width=300"
    },
    {
      name: "<PERSON>",
      specialty: "Digital Media",
      bio: "Digital content strategist with experience at major studios",
      image: "/placeholder.svg?height=300&width=300"
    },
    {
      name: "<PERSON>",
      specialty: "Screenwriting",
      bio: "Published screenwriter with multiple produced scripts",
      image: "/placeholder.svg?height=300&width=300"
    }
  ]

  return (
    <div className="min-h-screen bg-black text-white pt-24">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-5xl font-bold mb-8">Our <span className='text-brand'>Instructors</span> </h1>
        <p className="text-xl text-gray-300 mb-12">
          Learn from industry professionals with years of experience and expertise.
        </p>

        {/* Featured Video Section */}
        <div className="mb-16">
          <div className="mx-auto w-full max-w-[1280px]">
            <div className="relative w-full aspect-video max-h-[920px]">
              <iframe
                className="absolute top-0 left-0 w-full h-full rounded-lg border-0 shadow-2xl"
                src="https://www.youtube.com/embed/hj-XSCqFmZ8"
                title="Join the Del-York Creative Academy Family"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* {instructors.map((instructor, index) => (
            <div key={index} className="text-center">
              <Image
                src={instructor.image || "/placeholder.svg"}
                alt={instructor.name}
                width={300}
                height={300}
                className="w-full aspect-square object-cover rounded-lg mb-4"
              />
              <h3 className="text-xl font-bold mb-2">{instructor.name}</h3>
              <p className="text-gray-400 mb-2">{instructor.specialty}</p>
              <p className="text-gray-300 text-sm">{instructor.bio}</p>
            </div>
          ))} */}
        </div>
      </div>
    </div>
  )
}
