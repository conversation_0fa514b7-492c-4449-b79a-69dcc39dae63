import Image from "next/image";
import Link from "next/link";
import { Star } from "lucide-react";
import HeroSlider from "@/components/hero-slider";

export default function HomePage() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Hero Slider Section */}
      <HeroSlider />

      {/* About Section */}
      <section className="py-20 px-2">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-montserrat font-bold mb-6">
                About Del York Creative Academy
              </h2>
              <p className="text-gray-300 text-lg leading-relaxed mb-6">
                Del-York Creative Academy is Africa's premier institution for
                creative education, offering world-class training in filmmaking,
                acting, and digital media production.
              </p>
              <p className="text-gray-300 text-lg leading-relaxed">
                Our programs are designed to nurture creative talent and provide
                practical skills that meet international industry standards.
              </p>
            </div>
            <div>
              <Image
                src="/images/faculty.jpeg?height=800&width=800"
                alt="Creative Academy"
                width={800}
                height={800}
                className="rounded-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Live Learn Create Section */}
      <section className="p-20">
        <div className="bg-red-700 min-h-[500px]">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-4 h-full">
              {/* Live Learn Create Panel */}
              <div className="bg-red-700 p-8 lg:p-12 flex flex-col justify-center text-white">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-4xl lg:text-5xl font-bebas font-bold leading-tight tracking-wider">
                      LIVE
                      <br />
                      LEARN
                      <br />
                      CREATE
                    </h2>
                    <div className="w-16 h-1 bg-white mt-4"></div>
                  </div>
                  <p className="text-lg leading-relaxed">
                    We are empowering a generation of exceptional talents that
                    will advance Africa forward through film, media and
                    technology innovation.
                  </p>
                </div>
              </div>

              {/* Explore Panel */}
              <div className="bg-black p-8 lg:p-12 flex flex-col justify-between text-white relative overflow-hidden">
                <div className="absolute inset-0 opacity-30">
                  <Image
                    src="/images/students-on-camera.jpeg?height=500&width=400"
                    alt="Film Equipment"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative z-10 flex-1 flex items-center justify-center">
                  <button className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all">
                    <div className="w-0 h-0 border-l-[20px] border-l-white border-t-[12px] border-t-transparent border-b-[12px] border-b-transparent ml-1"></div>
                  </button>
                </div>
                <div className="relative z-10">
                  <h3 className="text-3xl font-montserrat font-light">
                    explore
                  </h3>
                </div>
              </div>

              {/* Online School Panel */}
              <div
                className="p-8 lg:p-12 flex flex-col justify-between text-white"
                style={{
                  backgroundImage: 'url("images/ibk-dca-student.png")',
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "cover",
                }}
              >
                <div className="space-y-6 text-center">
                  <div>
                    <div className="flex  space-x-2 mb-4">
                      <div className="w-8 h-8 bg-red-600 rounded-full flex ">
                        <div className="w-4 h-4 bg-white rounded-full"></div>
                      </div>
                      <span className="text-red-500 font-bebas font-semibold tracking-wider items-center">
                        ONLINE COURSES
                      </span>
                    </div>
                    <h3 className="text-2xl  font-montserrat font-bold mb-6">
                      Learn Online With
                      <br />
                      DCA
                    </h3>
                  </div>

                  <Link
                    href="/special-programs/online"
                    className="inline-block border border-white px-6 py-2 text-center items-center  justify-center text-sm font-bebas tracking-wider hover:bg-white hover:text-black transition-colors"
                  >
                    Read More
                  </Link>
                </div>

                <div className="mt-4 text-center">
                  <h4 className="text-3xl font-montserrat font-light  text-red-600">
                    online
                    <br />
                    school
                  </h4>
                </div>
              </div>

              {/* Statistics Panel */}
              <div className="bg-black p-8 lg:p-12 flex flex-col justify-between text-white">
                <div className="space-y-8">
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-3 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg
                        className="w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                      </svg>
                    </div>
                    <div className="text-4xl font-montserrat font-bold">
                      10,000+
                    </div>
                    <div className="text-sm text-gray-300 font-bebas tracking-wider">
                      STUDENTS TRAINED
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-3 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg
                        className="w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="text-4xl font-montserrat font-bold">
                      25+
                    </div>
                    <div className="text-sm text-gray-300 font-bebas tracking-wider">
                      TOP HOLLYWOOD
                      <br />
                      INSTRUCTORS
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-3 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <svg
                        className="w-6 h-6"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="text-sm text-gray-300 font-bebas tracking-wider mb-1">
                      GROWING IMPACT IN
                    </div>
                    <div className="text-4xl font-montserrat font-bold">
                      10+
                    </div>
                    <div className="text-sm text-gray-300 font-bebas tracking-wider">
                      AFRICAN
                      <br />
                      COUNTRIES
                    </div>
                  </div>
                </div>

                <div className="text-center mt-4">
                  <h4 className="text-3xl font-montserrat font-light">
                    DCA in
                    <br />
                    numbers
                  </h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Africa's Story Section */}
      <section className=" px-4 bg-white text-black">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <div className="space-y-6">
              <div>
                <h2 className="text-5xl lg:text-7xl font-montserrat font-bold leading-tight mb-4">
                  Africa's
                  <br />
                  foremost
                  <br />
                  capacity
                  <br />
                  building
                  <br />
                  institution
                </h2>
              </div>

              <div className="space-y-2">
                <p className="text-2xl lg:text-3xl font-montserrat font-medium">
                  It's Time To Rewrite Our
                </p>
                <p
                  className="text-4xl lg:text-5xl font-montserrat font-bold text-red-600 italic"
                  style={{ fontFamily: "cursive" }}
                >
                  Story.
                </p>
              </div>

              <div className="pt-4">
                <p className="text-lg font-bebas font-semibold tracking-wider">
                  FOR THE FILM, MEDIA, TECHNOLOGY AND
                  <br />
                  MARKETING COMMUNICATIONS INDUSTRY
                </p>
              </div>
            </div>

            {/* Image Content */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                <Image
                  src="/images/camera-hand.png?height=500&width=400&text=Hand+holding+professional+camera"
                  alt="African hand holding professional camera - representing ownership of storytelling"
                  width={500}
                  height={500}
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Stats Section */}
      <section className="py-20 px-4 bg-gray-100">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-montserrat font-bold text-center mb-12 text-black">
            Our Impact Across Africa
          </h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-montserrat font-bold text-red-600 mb-2">
                15+
              </div>
              <p className="text-black font-medium">Countries Reached</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-montserrat font-bold text-red-600 mb-2">
                500+
              </div>
              <p className="text-black font-medium">Films Produced</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-montserrat font-bold text-red-600 mb-2">
                100+
              </div>
              <p className="text-black font-medium">Industry Partners</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-montserrat font-bold text-red-600 mb-2">
                85%
              </div>
              <p className="text-black font-medium">Graduate Employment Rate</p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 px-4 bg-black text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-montserrat font-bold mb-6">
            Ready to Rewrite Your Story?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Join Africa's leading creative minds and become part of the
            generation that's reshaping the continent's narrative through film,
            media, and technology.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/register"
              className="bg-red-600 text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-red-700 transition-colors"
            >
              START YOUR JOURNEY
            </Link>
            <Link
              href="/courses"
              className="border border-white text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-white hover:text-black transition-colors"
            >
              EXPLORE PROGRAMS
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-gray-900">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-montserrat font-bold mb-4">
              What Our Students Say
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Hear from our graduates who have transformed their creative
              passion into successful careers
            </p>
          </div>

          {/* Featured Testimonial */}
          <div className="mb-16">
            <div className="bg-black rounded-2xl p-8 lg:p-12 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-red-600 opacity-10 rounded-full -mr-16 -mt-16"></div>
              <div className="relative z-10">
                <div className="flex flex-col lg:flex-row items-center gap-8">
                  <div className="flex-shrink-0">
                    <Image
                      src="/placeholder.svg?height=120&width=120&text=Sarah+Johnson"
                      alt="Sarah Johnson"
                      width={120}
                      height={120}
                      className="rounded-full border-4 border-red-600"
                    />
                  </div>
                  <div className="flex-1 text-center lg:text-left">
                    <div className="mb-4">
                      <div className="flex justify-center lg:justify-start mb-2">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className="h-5 w-5 text-yellow-400 fill-current"
                          />
                        ))}
                      </div>
                      <blockquote className="text-xl lg:text-2xl text-gray-100 italic leading-relaxed">
                        "Del York Creative Academy didn't just teach me
                        filmmaking techniques – it transformed my entire
                        approach to storytelling. The hands-on experience with
                        industry professionals gave me the confidence to direct
                        my first feature film, which premiered at the Lagos
                        International Film Festival."
                      </blockquote>
                    </div>
                    <div>
                      <div className="font-montserrat font-bold text-lg">
                        Sarah Johnson
                      </div>
                      <div className="text-red-500 font-bebas tracking-wider">
                        Professional Filmmaking Graduate
                      </div>
                      <div className="text-gray-400">
                        Now Director at Nollywood Studios
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Testimonials Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {/* Testimonial 1 */}
            <div className="bg-black rounded-lg p-6 relative">
              <div className="absolute top-4 right-4">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
              </div>
              <div className="mb-4">
                <Image
                  src="/images/testimonials/adeyemi-okanlawon.jpg?height=60&width=60&text=Michael+A"
                  alt="Michael Adebayo"
                  width={60}
                  height={60}
                  className="rounded-full mb-4"
                />
                <blockquote className="text-gray-300 italic mb-4">
                  "Attending the Del-York Creative Academy opened my eyes to the
                  immense opportunities within the Nigerian film industry. Also,
                  the network of relationships with professionals and peers
                  formed during the training has been invaluable to my growth in
                  the industry. The Acting for Film course gave me the
                  foundational tools upon which I have built a fulfilling career
                  first as an actor and now as a writer, director and producer.
                  "
                </blockquote>
              </div>
              <div>
                <div className="font-montserrat font-semibold">
                  Deyemi Okanlawon
                </div>
                <div className="text-sm text-red-500 font-bebas tracking-wider">
                  Acting Graduate
                </div>
                <div className="text-sm text-gray-400">
                  Nollywood Actor | Filmmaker
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-black rounded-lg p-6 relative">
              <div className="absolute top-4 right-4">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
              </div>
              <div className="mb-4">
                <Image
                  src="/images/testimonials/tamara-eteimo.jpeg?height=60&width=60&text=Fatima+H"
                  alt="Fatima Hassan"
                  width={60}
                  height={60}
                  className="rounded-full mb-4"
                />
                <blockquote className="text-gray-300 italic mb-4">
                  "Del-York creative Academy has imparted immensely in my career
                  and has enabled me to push my dreams forward to the place of
                  success and fame. I had my major break after emerging the
                  winner of the next movie star reality tv show by Sola Fajobi
                  in 2011 after graduating from DEL-YORK CREATIVE ACADEMY where
                  I graduated as the best acting student in 2011. "
                </blockquote>
              </div>
              <div>
                <div className="font-montserrat font-semibold">
                  Best Acting Student 2011
                </div>
                <div className="text-sm text-red-500 font-bebas tracking-wider">
                  Tamara Eteimo
                </div>
                <div className="text-sm text-gray-400">Actress</div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-black rounded-lg p-6 relative">
              <div className="absolute top-4 right-4">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
              </div>
              <div className="mb-4">
                <Image
                  src="/images/testimonials/rachael-bakam.jpeg?height=60&width=60&text=Rachael+B"
                  alt="Rachael Bakam"
                  width={60}
                  height={60}
                  className="rounded-full mb-4"
                />
                <blockquote className="text-gray-300 italic mb-4">
                  "Del-York Creative training impacted my capabilities as an
                  actress, filmmaker and film producer. It also improved my
                  skills in cinematography even for my television programmes.
                  The level of impact of the training can be seen from my
                  numerous achievements. I have grown after the Del-York
                  Creative Academy experience. "
                </blockquote>
              </div>
              <div>
                <div className="font-montserrat font-semibold">
                  Cinematography Graduate
                </div>
                <div className="text-sm text-red-500 font-bebas tracking-wider">
                  Rachael Bakam
                </div>
                <div className="text-sm text-gray-400">
                  Actress | TV Host | Writer | Editor
                </div>
              </div>
            </div>
          </div>

          {/* Stats Row */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-montserrat font-bold text-red-500 mb-2">
                4.9/5
              </div>
              <div className="text-gray-300 text-sm">Average Rating</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-montserrat font-bold text-red-500 mb-2">
                95%
              </div>
              <div className="text-gray-300 text-sm">Would Recommend</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-montserrat font-bold text-red-500 mb-2">
                1,200+
              </div>
              <div className="text-gray-300 text-sm">Success Stories</div>
            </div>
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-montserrat font-bold text-red-500 mb-2">
                85%
              </div>
              <div className="text-gray-300 text-sm">Career Placement</div>
            </div>
          </div>

          {/* Video Testimonials Preview */}
          <div className="bg-black rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-montserrat font-bold mb-4">
              Watch Student Success Stories
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              See how our graduates are making their mark in the creative
              industry across Africa and beyond.
            </p>
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="relative group cursor-pointer">
                <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=200&width=300&text=Video+Testimonial+1"
                    alt="Student Success Story 1"
                    width={300}
                    height={200}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center group-hover:bg-opacity-60 transition-all">
                    <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                      <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                    </div>
                  </div>
                </div>
                <div className="mt-3 text-sm">
                  <div className="font-montserrat font-semibold">
                    From Student to Director
                  </div>
                  <div className="text-gray-400">James Wilson's Journey</div>
                </div>
              </div>

              <div className="relative group cursor-pointer">
                <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=200&width=300&text=Video+Testimonial+2"
                    alt="Student Success Story 2"
                    width={300}
                    height={200}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center group-hover:bg-opacity-60 transition-all">
                    <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                      <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                    </div>
                  </div>
                </div>
                <div className="mt-3 text-sm">
                  <div className="font-montserrat font-semibold">
                    Breaking into Hollywood
                  </div>
                  <div className="text-gray-400">Amara's Success Story</div>
                </div>
              </div>

              <div className="relative group cursor-pointer">
                <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                  <Image
                    src="/placeholder.svg?height=200&width=300&text=Video+Testimonial+3"
                    alt="Student Success Story 3"
                    width={300}
                    height={200}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center group-hover:bg-opacity-60 transition-all">
                    <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                      <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                    </div>
                  </div>
                </div>
                <div className="mt-3 text-sm">
                  <div className="font-montserrat font-semibold">
                    Building a Media Empire
                  </div>
                  <div className="text-gray-400">
                    Kemi's Entrepreneurial Journey
                  </div>
                </div>
              </div>
            </div>

            <Link
              href="/testimonials"
              className="inline-block bg-red-600 text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-red-700 transition-colors"
            >
              VIEW ALL TESTIMONIALS
            </Link>
          </div>
        </div>
      </section>

      {/* Courses Showcase Section */}
      <section className="py-20 px-4 bg-gray-900">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-end mb-12">
            <h2 className="text-6xl lg:text-8xl font-montserrat font-bold text-white">
              <span className="font-montserrat font-light bg-red-600 p-4 text-[1.5rem]">
                Filmmaking
              </span>
              <br />
              courses
            </h2>
            <Link
              href="/courses"
              className="bg-red-600 text-white px-6 py-3 text-lg font-bebas tracking-wider hover:bg-red-700 transition-colors"
            >
              All Courses
            </Link>
          </div>

          {/* Course Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Row 1 */}
            <Link
              href="/courses/makeup-and-special-effects"
              className="relative group cursor-pointer overflow-hidden rounded-md border-2 border-gray-600"
            >
              <div className="aspect-[4/3] relative">
                <Image
                  src="/images/on-premise/makeup-and-effects-new1.jpg?height=300&width=400&text=Makeup+Artist+Working"
                  alt="Makeup and Special Effects Course"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all duration-300"></div>

                {/* Default Title */}
                <div className="absolute bottom-4 left-4 right-4 group-hover:opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-montserrat font-bold">
                    Makeup And Special Effects
                  </h3>
                </div>

                {/* Slide-up Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/90 to-transparent p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-out">
                  <h3 className="text-white text-xl font-montserrat font-bold mb-3">
                    Makeup And Special Effects
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Master professional makeup techniques and special effects
                    for film and television production.
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>4 weeks</span>
                    <span>In-Person</span>
                  </div>
                  <div className="w-full bg-red-600 text-white py-2 px-4 text-sm font-bebas tracking-wider hover:bg-red-700 transition-colors text-center">
                    Learn More
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/courses/3d-animation"
              className="relative group cursor-pointer overflow-hidden rounded-md border-2 border-gray-600"
            >
              <div className="aspect-[4/3] relative">
                <Image
                  src="/images/on-premise/3d-animation1.jpg?height=300&width=400&text=3D+Animation+Studio"
                  alt="3D Animation Course"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all duration-300"></div>

                {/* Default Title */}
                <div className="absolute bottom-4 left-4 right-4 group-hover:opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-montserrat font-bold">
                    3D Animation
                  </h3>
                </div>

                {/* Slide-up Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/90 to-transparent p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-out">
                  <h3 className="text-white text-xl font-montserrat font-bold mb-3">
                    3D Animation
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Create stunning 3D animations using industry-standard
                    software and professional techniques.
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>4 weeks</span>
                    <span>In-Person</span>
                  </div>
                  <div className="w-full bg-red-600 text-white py-2 px-4 text-sm font-bebas tracking-wider hover:bg-red-700 transition-colors text-center">
                    Learn More
                  </div>
                </div>
              </div>
            </Link>

            <Link
              href="/courses/acting-for-film"
              className="relative group cursor-pointer overflow-hidden rounded-md border-2 border-gray-600"
            >
              <div className="aspect-[4/3] relative">
                <Image
                  src="/images/on-premise/acting-for-film-alt.jpg?height=300&width=400&text=Acting+Class+Students"
                  alt="Acting For Film Course"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all duration-300"></div>

                {/* Default Title */}
                <div className="absolute bottom-4 left-4 right-4 group-hover:opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-montserrat font-bold">
                    Acting For Film
                    <br />
                    <span className="text-sm font-normal">(Online Course)</span>
                  </h3>
                </div>

                {/* Slide-up Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/90 to-transparent p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-out">
                  <h3 className="text-white text-xl font-montserrat font-bold mb-3">
                    Acting For Film
                    <span className="block text-sm font-normal text-red-400">
                      Online Course
                    </span>
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Develop your acting skills with professional techniques for
                    film and television performance.
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>4 weeks</span>
                    <span>Online</span>
                  </div>
                  <div className="w-full bg-red-600 text-white py-2 px-4 text-sm font-bebas tracking-wider hover:bg-red-700 transition-colors text-center">
                    Learn More
                  </div>
                </div>
              </div>
            </Link>

            <div className="relative group cursor-pointer overflow-hidden rounded-md border-2 border-gray-600">
              <div className="aspect-[4/3] relative">
                <Image
                  src="/images/on-premise/broadcast-journalism.jpg?height=300&width=400&text=Broadcast+Journalism+Studio"
                  alt="Broadcast Journalism Course"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all duration-300"></div>

                {/* Default Title */}
                <div className="absolute bottom-4 left-4 right-4 group-hover:opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-montserrat font-bold">
                    Broadcast Journalism
                    <br />
                    <span className="text-sm font-normal">(Online Course)</span>
                  </h3>
                </div>

                {/* Slide-up Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/90 to-transparent p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-out">
                  <h3 className="text-white text-xl font-montserrat font-bold mb-3">
                    Broadcast Journalism
                    <span className="block text-sm font-normal text-red-400">
                      Online Course
                    </span>
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Learn professional broadcast journalism skills including
                    reporting, anchoring, and production.
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>4 weeks</span>
                    <span>Online</span>
                  </div>
                  <button className="w-full bg-red-600 text-white py-2 px-4 text-sm font-bebas tracking-wider hover:bg-red-700 transition-colors">
                    Learn More
                  </button>
                </div>
              </div>
            </div>

            {/* Row 2 */}
            <div className="relative group cursor-pointer overflow-hidden rounded-md border-2 border-gray-600">
              <div className="aspect-[4/3] relative">
                <Image
                  src="/images/on-premise/colouring-and-grading.jpg?height=300&width=400&text=Film+Production+Set"
                  alt="Coloring and Grading"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all duration-300"></div>

                {/* Default Title */}
                <div className="absolute bottom-4 left-4 right-4 group-hover:opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-montserrat font-bold">
                    Coloring and Grading
                  </h3>
                </div>

                {/* Slide-up Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/90 to-transparent p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-out">
                  <h3 className="text-white text-xl font-montserrat font-bold mb-3">
                    Coloring and Grading
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Colorists play an integral role in ensuring that the visual
                    integrity of a film is maintained.
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>4 weeks</span>
                    <span>In-Person</span>
                  </div>
                  <button className="w-full bg-red-600 text-white py-2 px-4 text-sm font-bebas tracking-wider hover:bg-red-700 transition-colors">
                    Learn More
                  </button>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer overflow-hidden rounded-lg border-2 border-gray-600">
              <div className="aspect-[4/3] relative">
                <Image
                  src="/images/on-premise/dca-film-editing-1.jpg?height=300&width=400&text=Video+Editing+Suite"
                  alt="Video Editing Course"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all duration-300"></div>

                {/* Default Title */}
                <div className="absolute bottom-4 left-4 right-4 group-hover:opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-montserrat font-bold">
                    Video Editing &<br />
                    Post-Production
                  </h3>
                </div>

                {/* Slide-up Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/90 to-transparent p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-out">
                  <h3 className="text-white text-xl font-montserrat font-bold mb-3">
                    Video Editing & Post-Production
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Master professional video editing and post-production
                    techniques using industry-standard software.
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>4 weeks</span>
                    <span>In-Person</span>
                  </div>
                  <button className="w-full bg-red-600 text-white py-2 px-4 text-sm font-bebas tracking-wider hover:bg-red-700 transition-colors">
                    Learn More
                  </button>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer overflow-hidden rounded-lg border-2 border-gray-600">
              <div className="aspect-[4/3] relative">
                <Image
                  src="/images/on-premise/cinematography.jpg?height=300&width=400&text=Photography+Studio"
                  alt="Photography Course"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all duration-300"></div>

                {/* Default Title */}
                <div className="absolute bottom-4 left-4 right-4 group-hover:opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-montserrat font-bold">
                    Photography &<br />
                    Cinematography
                  </h3>
                </div>

                {/* Slide-up Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/90 to-transparent p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-out">
                  <h3 className="text-white text-xl font-montserrat font-bold mb-3">
                    Photography & Cinematography
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Learn professional photography and cinematography techniques
                    for both still and motion pictures.
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>4 weeks</span>
                    <span>In-Person</span>
                  </div>
                  <button className="w-full bg-red-600 text-white py-2 px-4 text-sm font-bebas tracking-wider hover:bg-red-700 transition-colors">
                    Learn More
                  </button>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer overflow-hidden rounded-lg border-2 border-gray-600">
              <div className="aspect-[4/3] relative">
                <Image
                  src="/images/on-premise/sound-design.jpg?height=300&width=400&text=Photography+Studio"
                  alt="Sound Design Course"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-60 transition-all duration-300"></div>

                {/* Default Title */}
                <div className="absolute bottom-4 left-4 right-4 group-hover:opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-montserrat font-bold">
                    Sound Design & <br />
                    Scoring For Film
                  </h3>
                </div>

                {/* Slide-up Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black via-black/90 to-transparent p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500 ease-out">
                  <h3 className="text-white text-xl font-montserrat font-bold mb-3">
                    Sound Design & Scoring For Film
                  </h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Create professional sound design and audio production for
                    film, television, and digital media..
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>4 weeks</span>
                    <span>In-Person</span>
                  </div>
                  <button className="w-full bg-red-600 text-white py-2 px-4 text-sm font-bebas tracking-wider hover:bg-red-700 transition-colors">
                    Learn More
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-12 text-center">
            <p className="text-gray-300 text-lg mb-6">
              Explore our comprehensive range of creative programs designed to
              prepare you for success in the entertainment industry.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/courses"
                className="bg-white text-black px-8 py-3 text-lg font-bebas tracking-wider hover:bg-gray-100 transition-colors"
              >
                VIEW ALL COURSES
              </Link>
              <Link
                href="/special-programs/online"
                className="border border-white text-white px-8 py-3 text-lg font-bebas tracking-wider hover:bg-white hover:text-black transition-colors"
              >
                ONLINE PROGRAMS
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
