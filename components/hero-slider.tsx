"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  PlayCircle,
} from "lucide-react";

const slides = [
  {
    id: 1,
    type: "video",
    media:
      "/dca-logo-video.mp4?height=1080&width=1920&text=Digital+Media+Creation",
    title: "",
    subtitle: "",
    description: "",
    cta: {
      primary: {
        text: "",
        href: "",
      },
      secondary: { text: "", href: "" },
    },
    overlay: "",
  },
  {
    id: 2,
    type: "image",
    media:
      "/images/sliders/slider-01.jpg?height=1080&width=1920&text=Film+Production+Studio",
    title: "Master the Art of Filmmaking",
    subtitle: "Professional Film Production Program",
    description:
      "Learn from industry experts and create compelling stories that captivate audiences worldwide. Our comprehensive filmmaking program covers everything from pre-production to post-production.",
    cta: {
      primary: { text: "EXPLORE FILMMAKING", href: "/courses/filmmaking" },
      secondary: { text: "WATCH DEMO", href: "#" },
    },
    overlay: "",
  },
  {
    id: 3,
    type: "image",
    media:
      "/images/sliders/slider-03-alt.jpeg?height=1080&width=1920&text=Acting+Performance+Stage",
    title: "Transform Into Any Character",
    subtitle: "Method Acting & Performance",
    description:
      "Discover the power of authentic performance through our intensive acting programs. Train with Hollywood professionals and develop the skills to bring any character to life.",
    cta: {
      primary: {
        text: "START ACTING JOURNEY",
        href: "/courses/acting-for-film",
      },
      secondary: { text: "VIEW PORTFOLIO", href: "/alumni" },
    },
    overlay: "bg-black/50",
  },

  {
    id: 4,
    type: "image",
    media:
      "/images/sliders/slider-02.jpg?height=1080&width=1920&text=Creative+Academy+Campus",
    title: "Your Creative Journey Starts Here!",
    subtitle: "Join Africa's Premier Creative Academy",
    description:
      "Be part of a community that's reshaping Africa's creative landscape. With world-class facilities and industry connections, your creative dreams become reality.",
    cta: {
      primary: { text: "APPLY NOW", href: "/register" },
      secondary: { text: "SCHEDULE TOUR", href: "/contact" },
    },
    overlay: "bg-black/50",
  },
];

export default function HeroSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Auto-play functionality
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
    }, 6000); // Change slide every 6 seconds

    return () => clearInterval(interval);
  }, [isPlaying, slides.length]); // Remove currentSlide from dependencies

  const nextSlide = () => {
    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev + 1) % slides.length);
    setTimeout(() => setIsTransitioning(false), 500);
  };

  const prevSlide = () => {
    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
    setTimeout(() => setIsTransitioning(false), 500);
  };

  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentSlide) return;
    setIsTransitioning(true);
    setCurrentSlide(index);
    setTimeout(() => setIsTransitioning(false), 500);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  return (
    <section className="relative h-screen overflow-hidden">
      {/* Slides Container */}
      <div className="relative h-full">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
              index === currentSlide
                ? "opacity-100 scale-100"
                : "opacity-0 scale-105"
            }`}
          >
            {/* Background Media */}
            <div className="absolute inset-0">
              {slide.type === "video" ? (
                <video
                  className="w-full h-full object-cover"
                  autoPlay
                  muted
                  loop
                  playsInline
                  poster={slide.poster}
                >
                  <source src={slide.media} type="video/mp4" />
                  {/* Fallback image if video fails to load */}
                  <Image
                    src={slide.poster || "/placeholder.svg"}
                    alt={slide.title}
                    fill
                    className="object-cover"
                  />
                </video>
              ) : (
                <Image
                  src={slide.media || "/placeholder.svg"}
                  alt={slide.title}
                  fill
                  className="object-cover object-top"
                  priority={index === 0}
                />
              )}
              <div className={`absolute inset-0 ${slide.overlay}`} />
            </div>

            {/* Content */}
            <div className="relative z-10 h-full flex items-center">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                <div className="max-w-3xl">
                  <div
                    className={`transform transition-all duration-1000 delay-300 ${
                      index === currentSlide
                        ? "translate-y-0 opacity-100"
                        : "translate-y-8 opacity-0"
                    }`}
                  >
                    {/* Subtitle */}
                    {slide.subtitle && (
                      <div className="mb-4">
                        <span
                          className={`inline-block px-4 py-2 text-sm font-montserrat font-light tracking-widest uppercase ${
                            slide.type === "video"
                              ? "bg-red-600/90 text-white backdrop-blur-sm"
                              : "bg-red-600 text-white"
                          }`}
                        >
                          {slide.subtitle}
                        </span>
                      </div>
                    )}

                    {/* Title */}

                    {slide.title && (
                      <h1 className="text-4xl md:text-5xl lg:text-6xl font-montserrat font-bold text-white mb-6 leading-tight">
                        {slide.title}
                      </h1>
                    )}

                    {/* Description */}
                    {slide.description && (
                      <p className="text-xl md:text-xl text-gray-200 mb-8 max-w-3xl leading-relaxed">
                        {slide.description}
                      </p>
                    )}

                    {/* CTAs */}
                    <div className="flex md:flex-row sm:flex-row gap-4">
                      {slide.cta.primary.text && (
                        <Link
                          href={slide.cta.primary.href}
                          className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 text-md font-montserrat font-light tracking-wider transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                        >
                          {slide.cta.primary.text}
                        </Link>
                      )}
                      {slide.cta.secondary.text && (
                        <Link
                          href={slide.cta.secondary.href}
                          className="border-2 border-white text-white hover:bg-white hover:text-black px-8 py-4 text-md font-montserrat font-light tracking-wider transition-all duration-300 transform hover:scale-105"
                        >
                          {slide.cta.secondary.text}
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Video Play/Pause Button (only for video slides) */}
            {slide.type === "video" && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  const video =
                    e.currentTarget.parentElement?.querySelector("video");
                  if (video) {
                    if (video.paused) {
                      video.play();
                    } else {
                      video.pause();
                    }
                  }
                }}
                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-20 bg-black/50 hover:bg-black/70 text-white p-4 rounded-full transition-all duration-300 hover:scale-110 backdrop-blur-sm"
              >
                {/* <PlayCircle className="h-8 w-8" /> */}
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        disabled={isTransitioning}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 text-white p-3 rounded-full transition-all duration-300 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>

      <button
        onClick={nextSlide}
        disabled={isTransitioning}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 text-white p-3 rounded-full transition-all duration-300 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-20 flex space-x-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            disabled={isTransitioning}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? "bg-red-600 scale-125"
                : "bg-white/50 hover:bg-white/75"
            }`}
          />
        ))}
      </div>

      {/* Play/Pause Control */}
      <button
        onClick={togglePlayPause}
        className="absolute bottom-8 right-8 z-20 bg-black/30 hover:bg-black/50 text-white p-3 rounded-full transition-all duration-300 hover:scale-110"
      >
        {isPlaying ? (
          <Pause className="h-5 w-5" />
        ) : (
          <Play className="h-5 w-5" />
        )}
      </button>

      {/* Progress Bar */}
      <div className="absolute bottom-0 left-0 right-0 z-20 h-1 bg-black/30">
        <div
          className="h-full bg-red-600 transition-all duration-300"
          style={{
            width: `${((currentSlide + 1) / slides.length) * 100}%`,
          }}
        />
      </div>

      {/* Slide Counter */}
      <div className="absolute top-8 right-8 z-20 bg-black/30 text-white px-4 py-2 rounded-full text-sm font-bebas tracking-wider">
        {String(currentSlide + 1).padStart(2, "0")} /{" "}
        {String(slides.length).padStart(2, "0")}
      </div>
    </section>
  );
}
