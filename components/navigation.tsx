"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { ChevronDown, Menu, X, ChevronRight } from "lucide-react";

// Types
interface MenuItem {
  href: string;
  name: string;
}

interface DropdownMenuProps {
  title: string;
  items: MenuItem[];
  position?: string;
  className?: string;
}

interface CourseCategory {
  title: string;
  items: MenuItem[];
}

// Course data
const FILM_MEDIA_COURSES: MenuItem[] = [
  { href: "/courses/acting-for-film", name: "Acting For Film (On-Premise)" },
  { href: "/courses/screenwriting", name: "Screenwriting For Film & TV (On-Premise)" },
  { href: "/courses/filmmaking", name: "Filmmaking/Directing" },
  { href: "/courses/post-production", name: "Post Production/Editing" },
  { href: "/courses/digital-content", name: "Digital Content Creation & Social Media Influencing" },
  { href: "/courses/public-relations", name: "Public Relations & Media Communications" },
  { href: "/courses/3d-animation", name: "3D Animation" },
  { href: "/courses/producing", name: "Producing & The Business of Film-Making" },
  { href: "/courses/fashion-arts", name: "Fashion, Arts & Craft" },
];

const MORE_COURSES: MenuItem[] = [
  { href: "/courses/music-video-production", name: "Music Video Production" },
  { href: "/courses/vfx", name: "VFX" },
  { href: "/courses/cinematography-lighting", name: "Cinematography & Set Lighting" },
  { href: "/courses/theater-production", name: "Intro To Theater Production" },
  { href: "/courses/makeup-and-special-effects", name: "Makeup & Special Effects" },
  { href: "/courses/costume-set-design", name: "Costume & Set Design" },
  { href: "/courses/digital-marketing", name: "Digital Marketing" },
  { href: "/courses/photography", name: "Photography" },
  { href: "/courses/colouring-grading", name: "Colouring & Grading" },
  { href: "/courses/drones", name: "Drones" },
  { href: "/courses/sound-design-scoring", name: "Sound Design & Scoring For Film" },
];

const ONLINE_COURSES: MenuItem[] = [
  { href: "/courses/acting-for-film-online", name: "Acting For Film (Online)" },
  { href: "/courses/broadcast-journalism-online", name: "Broadcast Journalism (Online)" },
  { href: "/courses/digital-marketing-online", name: "Digital Marketing (Online)" },
  { href: "/courses/screen-writing-online", name: "Screenwriting (Online)" },
  { href: "/courses/content-creation", name: "Content Creation & Social Media (Online)" },
  { href: "/courses/photography", name: "Photography Basics (Online)" },
  { href: "/courses/video-editing", name: "Video Editing Fundamentals (Online)" },
  { href: "/courses/online", name: "View All Online Programs" }, 
];

// Menu items configuration
const ABOUT_MENU_ITEMS: MenuItem[] = [
  { href: "/about/what-we-do", name: "What We Do" },
  { href: "/about/strategic-objectives", name: "Our Strategic Objectives" },
  { href: "/about/founder", name: "Founder" },
  { href: "/about/expressions", name: "Our Expressions" },
];

const SPECIAL_PROGRAMS_ITEMS: MenuItem[] = [
  { href: "/special-programs", name: "All Programs" },
  { href: "https://www.yappi.ng", name: "YAPPI" },
  { href: "https://www.creativelagos.ng", name: "Creative Lagos" },
  { href: "/special-programs/workshops", name: "Google Behind The Camera" },
  { href: "/special-programs/summer", name: "Africa No Filter (ANF)" },
  { href: "/special-programs/online", name: "Online Programs" },
];

const RESOURCES_ITEMS: MenuItem[] = [
  { href: "/news", name: "News & Updates" },
  { href: "/blog", name: "Blog" },
  { href: "/events", name: "Events" },
  { href: "/gallery", name: "Gallery" },
  { href: "/testimonials", name: "Testimonials" },
  { href: "/faq", name: "FAQ" },
  { href: "/downloads", name: "Downloads" },
];

const COMMUNITY_ITEMS: MenuItem[] = [
  { href: "/instructors", name: "Our Instructors" },
  { href: "/alumni", name: "Alumni Network" },
  { href: "/student-life", name: "Student Life" },
  { href: "/careers", name: "Career Services" },
  { href: "/mentorship", name: "Mentorship Program" },
];

const ADMISSIONS_ITEMS: MenuItem[] = [
  { href: "/register", name: "Apply Now" },
  { href: "/admissions/requirements", name: "Requirements" },
  { href: "/admissions/tuition", name: "Tuition & Fees" },
  { href: "/admissions/scholarships", name: "Scholarships" },
  { href: "/admissions/financial-aid", name: "Financial Aid" },
  { href: "/admissions/schedule", name: "Class Schedule" },
];

const MOBILE_MENU_ITEMS: MenuItem[] = [
  { href: "/about", name: "About" },
  { href: "/special-programs", name: "Special Programs" },
  { href: "/courses", name: "Courses" },
  { href: "/news", name: "News & Resources" },
  { href: "/instructors", name: "Instructors" },
  { href: "/alumni", name: "Alumni" },
  { href: "/register", name: "Apply Now" },
  { href: "/contact", name: "Contact" },
];

const FEATURED_COURSES = [
  {
    title: "Professional Filmmaking",
    description: "Complete training from script to screen with industry professionals.",
    duration: "20 weeks • In-Person",
    href: "/courses/filmmaking",
  },
  {
    title: "Acting For Film",
    description: "Master on-camera acting techniques with professional coaches.",
    duration: "10 weeks • Online Available",
    href: "/courses/acting-for-film",
  },
  {
    title: "3D Animation",
    description: "Create stunning animations using industry-standard software.",
    duration: "16 weeks • In-Person",
    href: "/courses/3d-animation",
  },
];

const COURSE_CATEGORIES: CourseCategory[] = [
  { title: "Film & Media Courses", items: FILM_MEDIA_COURSES },
  { title: "More", items: MORE_COURSES },
  { title: "Online Courses", items: ONLINE_COURSES },
];

// Reusable Components
function DropdownMenu({ title, items, position = "left-0", className = "" }: DropdownMenuProps) {
  return (
    <div className="relative group">
      <button className="text-white hover:text-gray-300 flex items-center space-x-1 transition-colors">
        <span>{title}</span>
        <ChevronDown className="h-4 w-4" />
      </button>
      <div className={`absolute top-full mt-2 w-56 bg-red-700 border border-red-600 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 ${position} ${className}`}>
        {items.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className="block px-4 py-3 text-white hover:bg-red-600 font-montserrat transition-colors"
            target={item.href.startsWith('http') ? '_blank' : undefined}
            rel={item.href.startsWith('http') ? 'noopener noreferrer' : undefined}
          >
            {item.name}
          </Link>
        ))}
      </div>
    </div>
  );
}

function CourseSubmenu({ category }: { category: CourseCategory }) {
  return (
    <div className="relative group/submenu">
      <button className="text-white font-montserrat font-semibold text-base mb-2 flex items-center justify-between w-full hover:text-red-200 transition-colors">
        <span>{category.title}</span>
        <ChevronRight className="h-4 w-4" />
      </button>
      <div className="absolute left-full top-0 ml-2 w-80 bg-red-700 border border-red-600 rounded-md p-4 opacity-0 invisible group-hover/submenu:opacity-100 group-hover/submenu:visible transition-all duration-300 z-60">
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {category.items.map((course, index) => (
            <Link
              key={course.href}
              href={course.href}
              className={`block text-white hover:text-red-200 font-montserrat text-base py-2 transition-colors ${
                index < category.items.length - 1 ? 'border-b border-red-600/30 hover:border-red-200/50' : ''
              }`}
              target={course.href.startsWith('http') ? '_blank' : undefined}
              rel={course.href.startsWith('http') ? 'noopener noreferrer' : undefined}
            >
              {course.name}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}

function FeaturedCourse({ course }: { course: typeof FEATURED_COURSES[0] }) {
  return (
    <div className="bg-red-600/20 rounded-lg p-4 border border-red-500/30 hover:bg-red-600/30 transition-colors">
      <h4 className="text-white font-montserrat font-bold text-lg mb-2">
        {course.title}
      </h4>
      <p className="text-red-100 text-sm mb-3">
        {course.description}
      </p>
      <div className="flex items-center justify-between">
        <span className="text-red-200 text-sm">
          {course.duration}
        </span>
        <Link
          href={course.href}
          className="text-white hover:text-red-200 text-sm font-bebas tracking-wider transition-colors"
        >
          LEARN MORE →
        </Link>
      </div>
    </div>
  );
}

function LanguageSwitcher() {
  return (
    <div className="flex items-center space-x-2">
      <button className="text-white hover:text-gray-300 transition-colors text-sm font-medium">
        EN
      </button>
      <span className="text-gray-500">|</span>
      <button className="text-gray-500 hover:text-white transition-colors text-sm font-medium">
        FR
      </button>
    </div>
  );
}

export default function Navigation() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <nav className="fixed w-full top-0 z-50 bg-black/90 backdrop-blur-sm  bg-[url('/images/page-header-bg.png')] bg-cover bg-no-repeat">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left Menu */}
          <div className="hidden lg:flex items-center space-x-8">
            <DropdownMenu title="About" items={ABOUT_MENU_ITEMS} />

            <div className="relative group">
              <button className="text-white hover:text-gray-300 flex items-center space-x-1 transition-colors">
                <span>Special Programs</span>
                <ChevronDown className="h-4 w-4" />
              </button>
              <div className="absolute top-full left-0 mt-2 w-56 bg-black border border-gray-800 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                {SPECIAL_PROGRAMS_ITEMS.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="block px-4 py-2 text-sm text-white hover:bg-gray-800 transition-colors"
                    target={item.href.startsWith('http') ? '_blank' : undefined}
                    rel={item.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>

            {/* Courses Mega Menu */}
            <div className="relative group">
              <button className="text-white hover:text-gray-300 flex items-center space-x-1 transition-colors">
                <span>Courses</span>
                <ChevronDown className="h-4 w-4" />
              </button>

              {/* Mega Menu Dropdown */}
              <div className="absolute top-full left-0 mt-2 w-[800px] bg-red-700 border border-red-600 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                <div className="grid grid-cols-12 min-h-[400px]">
                  {/* Left Sidebar - Categories */}
                  <div className="col-span-4 bg-red-800 p-6 rounded-l-md">
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-white font-montserrat font-bold text-lg mb-4">
                          Course Categories
                        </h3>
                      </div>
                      {COURSE_CATEGORIES.map((category) => (
                        <CourseSubmenu key={category.title} category={category} />
                      ))}
                    </div>
                  </div>

                  {/* Right Content - Featured Courses */}
                  <div className="col-span-8 p-6">
                    <h3 className="text-white font-montserrat font-bold text-xl mb-6">
                      Featured Courses
                    </h3>
                    <div className="grid grid-cols-1 gap-4">
                      {FEATURED_COURSES.map((course) => (
                        <FeaturedCourse key={course.href} course={course} />
                      ))}
                    </div>

                    {/* Bottom CTA */}
                    <div className="mt-6 pt-4 border-t border-red-600/30">
                      <Link
                        href="/courses"
                        className="inline-block bg-white text-red-700 px-6 py-2 font-bebas tracking-wider hover:bg-red-100 transition-colors rounded"
                      >
                        VIEW ALL COURSES
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <DropdownMenu title="Resources" items={RESOURCES_ITEMS} /> */}

          </div>

          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <Image
              src="/images/dca-logo.png"
              alt="Del York Creative Academy"
              width={120}
              height={60}
              className="h-12 w-auto"
            />
          </Link>

          {/* Right Menu */}
          <div className="hidden lg:flex items-center space-x-8">
            {/* <DropdownMenu title="Community" items={COMMUNITY_ITEMS} position="right-0" /> */}
            {/* <DropdownMenu title="Admissions" items={ADMISSIONS_ITEMS} position="right-0" /> */}

            <Link
              href="/contact"
              className="text-white hover:text-gray-300 transition-colors"
            >
              Contact
            </Link>

            <LanguageSwitcher />
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="text-white hover:text-gray-300 transition-colors"
              aria-label="Toggle mobile menu"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden bg-black/95 backdrop-blur-sm border-t border-gray-800">
            <div className="px-2 pt-2 pb-3 space-y-1 max-h-96 overflow-y-auto">
              {MOBILE_MENU_ITEMS.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="block px-3 py-2 text-white hover:bg-gray-800 transition-colors font-montserrat"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
